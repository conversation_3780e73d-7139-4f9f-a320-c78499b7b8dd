#include "../../include/business/mqtt_callback_business.h"
#include "../../include/controls/mqtt_control.h"

// 当前接收到的指纹数据包
String fpm383_packs[FPM383_PACK_NUM];

// MQTT 按键密码新增回调函数
void mqtt_callback_password_add(const JsonObjectConst &data)
{
  if (!data["id"].isNull() && !data["fx_id"].isNull() && !data["pwd_num"].isNull())
  {
    int id = data["id"];
    int fx_id = data["fx_id"];
    String pwd_num = data["pwd_num"];
    bool is_added = KeypadDatabase::getInstance().add(id, pwd_num);
    if (is_added)
    {
      KeypadDatabase::getInstance().store();
      mqtt_report_update(fx_id, true);
    }
    else
    {
      mqtt_report_update(fx_id, false);
    }
  }
}

// MQTT 按键密码删除回调函数
void mqtt_callback_password_remove(const JsonObjectConst &data)
{
  if (!data["id"].isNull() && !data["fx_id"].isNull() && !data["pwd_num"].isNull())
  {
    int id = data["id"];
    int fx_id = data["fx_id"];
    String pwd_num = data["pwd_num"];
    bool is_removed = KeypadDatabase::getInstance().remove(id, pwd_num);
    if (is_removed)
    {
      KeypadDatabase::getInstance().store();
      mqtt_report_update(fx_id, true);
    }
    else
    {
      mqtt_report_update(fx_id, false);
    }
  }
}

// MQTT 指纹新增回调函数
void mqtt_callback_fpm383_add(const JsonObjectConst &data)
{
  if (!data["id"].isNull() && !data["fx_id"].isNull() && !data["index"].isNull() && !data["total"].isNull() && !data["f_info"].isNull())
  {
    int id = data["id"];
    int fx_id = data["fx_id"];
    int index = data["index"];
    String f_info = data["f_info"];
    if (index >= 0 && index < FPM383_PACK_NUM)
    {
      if (index == 0 || (index > 0 && !(fpm383_packs[index - 1].isEmpty())))
      {
        fpm383_packs[index] = f_info;
        bool is_valid = true;
        for (int t = 0; t < FPM383_PACK_NUM; t++)
        {
          if (fpm383_packs[t].isEmpty())
          {
            is_valid = false;
            break;
          }
        }
        if (is_valid)
        {
          FPM383Status is_ready = fpm383_pre_download();
          if (is_ready == FPM383Status::SUCCESS)
          {
            fpm383_download_process(fpm383_packs);
            FPM383Status is_stored = fpm383_store(id);
            if (is_stored == FPM383Status::SUCCESS)
            {
              for (int t = 0; t < FPM383_PACK_NUM; t++)
              {
                fpm383_packs[t] = "";
              }
              mqtt_report_update(fx_id, true);
              return;
            }
          }
          mqtt_report_update(fx_id, false);
        }
      }
      else
      {
        for (int t = 0; t < FPM383_PACK_NUM; t++)
        {
          fpm383_packs[t] = "";
        }
        print_log("fpm383", "add", "error pack index received, clear all...");
      }
    }
    else
    {
      print_log("fpm383", "add", "invalid pack index!!");
    }
  }
}

// MQTT 指纹删除回调函数
void mqtt_callback_fpm383_remove(const JsonObjectConst &data)
{
  if (!data["id"].isNull() && !data["fx_id"].isNull())
  {
    int id = data["id"];
    int fx_id = data["fx_id"];
    FPM383Status is_removed = fpm383_remove(id);
    if (is_removed == FPM383Status::SUCCESS)
    {
      mqtt_report_update(fx_id, true);
    }
    else
    {
      mqtt_report_update(fx_id, false);
    }
  }
}

// MQTT 电机回调函数
void mqtt_callback_motor(const JsonObjectConst &data)
{
  if (!data["id"].isNull() && !data["fx_id"].isNull())
  {
    int id = data["id"];
    int fx_id = data["fx_id"];
    bool ok = on_unlock();
    if (ok) {
      OperationDatabase::getInstance().add(id, OPERATION_TYPE_MQTT, esp32_get_time());
      OperationDatabase::getInstance().store();
    }
    mqtt_report_update(fx_id, ok);
  }
}

// MQTT 公有主题回调函数
void mqtt_callback_public()
{
  print_log("mqtt", "callback", "public");
}

// MQTT 专有主题回调函数
void mqtt_callback_self(const DynamicJsonDocument &doc)
{
  print_log("mqtt", "callback", "self");

  if (!doc["__command__"].isNull())
  {
    // 调试命令
    String __command__ = doc["__command__"];
    if (__command__ == "ota")
    {
      start_ota_update();
    }
    else if (__command__ == "counter")
    {
      mqtt_report_counter();
    }
  }
  else if (!doc["time"].isNull())
  {
    // 时间同步
    time_t current_time = doc["time"];
    esp32_set_time(current_time);
  }
  else if (!doc["is_action"].isNull())
  {
    // 更新提示
    String is_action = doc["is_action"];
    if (is_action == "start")
    {
      // voice_write(VOICE_UPDATING, sizeof(VOICE_UPDATING), 1500);
    }
    else if (is_action == "end")
    {
      voice_write(VOICE_UPDATED, sizeof(VOICE_UPDATED), 1500);
    }
  }
  else if (!doc["type"].isNull() && !doc["action"].isNull() && !doc["data"].isNull())
  {
    // 更新数据包
    String type = doc["type"];
    String action = doc["action"];
    JsonObjectConst data = doc["data"];
    if (type == "pwd" && action == "add")
    {
      mqtt_callback_password_add(data);
    }
    else if (type == "pwd" && action == "del")
    {
      mqtt_callback_password_remove(data);
    }
    else if (type == "fin" && action == "add")
    {
      mqtt_callback_fpm383_add(data);
    }
    else if (type == "fin" && action == "del")
    {
      mqtt_callback_fpm383_remove(data);
    }
    else if (type == "motor" && action == "turn")
    {
      mqtt_callback_motor(data);
    }
    sleep_report();
  }
}
